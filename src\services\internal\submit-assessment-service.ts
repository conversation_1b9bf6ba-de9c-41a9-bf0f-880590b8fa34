import logger from '@lcs/logger'
import { getErrorMessage } from '@tess-f/backend-utils'
import { EventType } from '@tess-f/shared-config'
import {
  SubmitAssessmentRequest,
  SubmitAssessmentResponse
} from '../../models/internal/submit-assessment.js'
import { SessionModel } from '../../models/session.model.js'
import QuestionResponseModel from '../../models/question-response.model.js'
import updateEvaluationSession from '../mssql/session/update.service.js'
import getEvaluationSession from '../mssql/session/get.service.js'
import createQuestionResponse from '../mssql/question-response/create.service.js'
import getEvaluation from '../mssql/evaluations/get.service.js'
import getSessionQuestions from '../mssql/session-questions/get.service.js'
import getQuestionWithOptions from '../mssql/questions/get-question-with-options.service.js'
import gradeQuestion from './auto-grade-service.js'
import mssql from '@lcs/mssql-utility'
import { SessionQuestionScore, SessionQuestionScoreFields, EvaluationSectionTableName as SessionQuestionScoreTableName } from '@tess-f/sql-tables/dist/evaluations/session-question-score.js'
import { QuestionResponse } from '@tess-f/sql-tables/dist/evaluations/question-response.js'
import { QuestionWithOptions } from '@tess-f/evaluations/dist/common/question.js'

const log = logger.create('submit-assessment-service')

/**
 * Gets all questions assigned to a specific evaluation session with their options
 */
async function getQuestionsForSession(sessionId: string): Promise<QuestionWithOptions[]> {
  // Get the session questions (questions assigned to this specific session)
  const sessionQuestions = await getSessionQuestions(sessionId)

  // Build each question with its options
  const questionsWithOptions: QuestionWithOptions[] = []
  for (const sessionQuestion of sessionQuestions) {
    const questionWithOptions = await getQuestionWithOptions(
      sessionQuestion.fields.QuestionId!,
      sessionQuestion.fields.QuestionVersion!
    )

    if (questionWithOptions) {
      questionsWithOptions.push(questionWithOptions)
    }
  }

  return questionsWithOptions
}

/**
 * Groups responses by question ID and version, ensuring sub-question responses
 * are grouped with their parent question for proper grading
 */
function groupResponsesByQuestion(
  responses: QuestionResponse[],
  evaluationQuestions: QuestionWithOptions[]
): Map<string, QuestionResponse[]> {
  const grouped = new Map<string, QuestionResponse[]>()
  
  // Create a map of sub-question ID to parent question for quick lookup
  const subQuestionToParent = new Map<string, { parentId: string; parentVersion: number }>()
  
  for (const question of evaluationQuestions) {
    if (question.SubQuestions) {
      for (const subQuestion of question.SubQuestions) {
        const subKey = `${subQuestion.Id}-${subQuestion.Version}`
        subQuestionToParent.set(subKey, {
          parentId: question.Id!,
          parentVersion: question.Version!
        })
      }
    }
  }
  
  for (const response of responses) {
    const responseKey = `${response.QuestionId}-${response.QuestionVersion}`
    
    // Check if this is a sub-question response
    const parentInfo = subQuestionToParent.get(responseKey)
    
    // Use parent question key if this is a sub-question, otherwise use the response's own key
    const groupKey = parentInfo 
      ? `${parentInfo.parentId}-${parentInfo.parentVersion}`
      : responseKey
    
    if (!grouped.has(groupKey)) {
      grouped.set(groupKey, [])
    }
    grouped.get(groupKey)!.push(response)
  }
  
  return grouped
}

/**
 * Calculates total score and determines pass/fail status
 */
async function calculateSessionResults(
  sessionId: string,
  evaluationQuestions: QuestionWithOptions[],
  evaluation: { PassingPercentage?: number | null; PassingScore?: number | null; MinimumScore?: number | null }
): Promise<{
  totalScore: number
  maxScore: number
  passed: boolean
  gradedCount: number
  pendingCount: number
}> {
  const request = mssql.getPool().request()
  request.input('sessionId', sessionId)

  const results = await request.query<Required<Pick<SessionQuestionScore, 'Score' | 'Pending'>>>(`
    SELECT
      [${SessionQuestionScoreFields.Score}],
      [${SessionQuestionScoreFields.Pending}]
    FROM [${SessionQuestionScoreTableName}]
    WHERE [${SessionQuestionScoreFields.SessionId}] = @sessionId
  `)

  let totalScore = 0
  let gradedCount = 0
  let pendingCount = 0

  for (const score of results.recordset) {
    if (score.Pending) {
      pendingCount++
    } else {
      // Use scores as-is (should already be whole numbers)
      totalScore += score.Score || 0
      gradedCount++
    }
  }

  // Calculate maxScore by summing the actual points from all evaluation questions
  const maxScore = evaluationQuestions.reduce((total, question) => {
    return total + (question.Points || 0)
  }, 0)

  // Use evaluation-specific passing score instead of hardcoded 70%
  let passingThreshold = 0.7 // Default fallback to 70%

  // Check for passing score in evaluation (could be percentage or absolute score)
  if (evaluation.PassingPercentage != null && evaluation.PassingPercentage > 0) {
    // PassingPercentage is likely stored as a decimal (0.7) or percentage (70)
    passingThreshold = evaluation.PassingPercentage > 1
      ? evaluation.PassingPercentage / 100
      : evaluation.PassingPercentage
  } else if (evaluation.PassingScore != null && evaluation.PassingScore > 0 && maxScore > 0) {
    // PassingScore is an absolute score, convert to percentage
    passingThreshold = evaluation.PassingScore / maxScore
  } else if (evaluation.MinimumScore != null && evaluation.MinimumScore > 0 && maxScore > 0) {
    // MinimumScore is an absolute score, convert to percentage
    passingThreshold = evaluation.MinimumScore / maxScore
  }

  const passed = maxScore > 0 ? (totalScore / maxScore) >= passingThreshold : false

  return {
    totalScore,
    maxScore,
    passed,
    gradedCount,
    pendingCount
  }
}

/**
 * Submits a completed assessment with student responses.
 * This service handles the complete submission workflow:
 * 1. Saves all student responses to the database
 * 2. Grades all questions automatically where possible
 * 3. Calculates total score and pass/fail status
 * 4. Updates the session with completion data
 *
 * Note: Input validation is handled at the controller level.
 *
 * @param request The validated assessment submission data
 * @returns Promise containing submission results and scoring information
 */
export default async function submitAssessment(
  request: SubmitAssessmentRequest
): Promise<SubmitAssessmentResponse> {
  try {
    const { sessionId, responses, endTime } = request

    // Type assertion: validation at controller level ensures these are defined
    const validSessionId = sessionId!
    const validEndTime = endTime!

    log('info', 'Starting assessment submission process', {
      sessionId: validSessionId,
      responsesCount: responses.length,
      eventType: EventType.evaluation_grade
    })

    // 2. Get session and evaluation data
    const session = await getEvaluationSession(validSessionId)
    const evaluation = await getEvaluation(session.fields.EvalId!)
    const evaluationQuestions = await getQuestionsForSession(validSessionId)

    // 3. Group responses by question for processing
    const responsesByQuestion = groupResponsesByQuestion(responses, evaluationQuestions)

    log('info', 'Processing question responses', {
      sessionId,
      uniqueQuestionsCount: responsesByQuestion.size,
      eventType: EventType.question_grade
    })

    // 4. Save responses and grade questions
    let processedResponses = 0
    for (const [questionKey, questionResponses] of responsesByQuestion) {
      const [questionId, questionVersionStr] = questionKey.split('-')
      const questionVersion = Number.parseInt(questionVersionStr)
      
      // Find the question definition
      const question = evaluationQuestions.find(q => 
        q.Id === questionId && q.Version === questionVersion
      )
      
      if (!question) {
        log('warn', 'Question not found in evaluation', {
          questionId,
          questionVersion,
          sessionId,
          eventType: EventType.question_grade
        })
        continue
      }

      // Convert to QuestionResponseModel instances and save
      const responseModels = questionResponses.map(resp =>
        new QuestionResponseModel({
          SessionId: sessionId,
          QuestionId: resp.QuestionId,
          QuestionVersion: resp.QuestionVersion,
          OptionId: resp.OptionId,
          OptionVersion: resp.OptionVersion,
          TargetOptionId: resp.TargetOptionId,
          TargetOptionVersion: resp.TargetOptionVersion,
          ResponseText: resp.ResponseText,
          Duration: resp.Duration,
          OrderId: resp.OrderId
        })
      )
      
      // Save responses to database
      for (const responseModel of responseModels) {
        await createQuestionResponse(responseModel)
      }
      
      // Grade the question
      await gradeQuestion(question, responseModels, validSessionId, evaluation.Id, evaluation.Version)
      processedResponses += questionResponses.length
    }

    // 5. Calculate final results
    const results = await calculateSessionResults(validSessionId, evaluationQuestions, evaluation)

    // 6. Update session with completion data
    await updateEvaluationSession(new SessionModel({
      Id: validSessionId,
      End: validEndTime,
      Score: results.totalScore,
      Passed: results.passed
    }))

    // 7. Return response
    const response: SubmitAssessmentResponse = {
      Id: validSessionId,
      Passed: results.passed,
      Score: results.totalScore,
      maxScore: results.maxScore,
      responsesCount: processedResponses,
      gradedQuestionsCount: results.gradedCount,
      pendingQuestionsCount: results.pendingCount
    }

    log('info', 'Successfully submitted assessment', {
      response,
      eventType: EventType.evaluation_grade
    })
    
    return response

  } catch (error) {
    log('error', 'Failed to submit assessment', {
      error: getErrorMessage(error),
      sessionId: request.sessionId!,
      eventType: EventType.evaluation_grade
    })
    throw error
  }
}